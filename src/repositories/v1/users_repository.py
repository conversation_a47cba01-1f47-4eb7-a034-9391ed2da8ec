#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 20/08/2025
"""
from src.common.choices import StatusChoice
from src.models.mongo import MongoBaseModel
from src.models.mongodb.users_model import UsersModel
from src.repositories.base_repository import BaseRepository


class UsersRepository(MongoBaseModel):

    def __init__(self):
        super().__init__()
        self.users_model = UsersModel()

    async def count_users(self):
        return await self.users_model.count({"status": StatusChoice.ACTIVATE.value})
