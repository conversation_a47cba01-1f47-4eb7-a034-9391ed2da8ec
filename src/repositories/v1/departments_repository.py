#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 20/08/2025
"""
from src.common.choices import StatusChoice
from src.models.mongo import MongoBaseModel
from src.models.mongodb.departments_model import DepartmentsModel


class DepartmentsRepository(MongoBaseModel):

    def __init__(self):
        super().__init__()
        self.departments_model = DepartmentsModel()

    async def count_departments(self):
        return await self.departments_model.count({"status": StatusChoice.ACTIVATE.value})